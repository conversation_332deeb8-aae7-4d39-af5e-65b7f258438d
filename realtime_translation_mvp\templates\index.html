<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时翻译MVP - 英文→中文</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            font-size: 1.1em;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }

        .status-text {
            font-size: 1.1em;
            color: #333;
            margin-bottom: 10px;
        }

        .audio-level {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .audio-level-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            width: 0%;
            transition: width 0.1s ease;
        }

        .controls {
            margin: 30px 0;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
            min-width: 150px;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn.stop {
            background: #dc3545;
        }

        .btn.stop:hover {
            background: #c82333;
        }

        .translations {
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
            text-align: left;
        }

        .translation-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            animation: slideIn 0.3s ease;
        }

        .translation-text {
            font-size: 1.2em;
            color: #333;
            line-height: 1.5;
        }

        .translation-meta {
            font-size: 0.9em;
            color: #666;
            margin-top: 8px;
            display: flex;
            justify-content: space-between;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎤 实时翻译</h1>
            <p class="subtitle">英文 → 中文 | 基于SeamlessM4T</p>
        </div>

        <div class="status-panel">
            <div class="status-text" id="statusText">系统初始化中...</div>
            <div class="audio-level">
                <div class="audio-level-bar" id="audioLevelBar"></div>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="translationCount">0</div>
                <div class="stat-label">翻译次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="avgLatency">0.0s</div>
                <div class="stat-label">平均延迟</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="systemStatus">离线</div>
                <div class="stat-label">系统状态</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="startBtn" onclick="startTranslation()">
                开始翻译
            </button>
            <button class="btn stop" id="stopBtn" onclick="stopTranslation()" disabled>
                停止翻译
            </button>
        </div>

        <div class="translations" id="translations">
            <div style="text-align: center; color: #666; padding: 20px;">
                点击"开始翻译"按钮，然后对着麦克风说英文
            </div>
        </div>
    </div>

    <script>
        // Socket.IO连接
        const socket = io();
        
        // 状态变量
        let isTranslating = false;
        let translationCount = 0;
        let totalLatency = 0;
        
        // DOM元素
        const statusText = document.getElementById('statusText');
        const audioLevelBar = document.getElementById('audioLevelBar');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const translations = document.getElementById('translations');
        const translationCountEl = document.getElementById('translationCount');
        const avgLatencyEl = document.getElementById('avgLatency');
        const systemStatusEl = document.getElementById('systemStatus');

        // Socket事件处理
        socket.on('connect', function() {
            console.log('已连接到服务器');
            updateStatus('已连接到服务器', 'success');
            systemStatusEl.textContent = '在线';
            systemStatusEl.style.color = '#28a745';
        });

        socket.on('disconnect', function() {
            console.log('与服务器断开连接');
            updateStatus('与服务器断开连接', 'error');
            systemStatusEl.textContent = '离线';
            systemStatusEl.style.color = '#dc3545';
        });

        socket.on('system_status', function(data) {
            if (data.ready) {
                updateStatus('系统已就绪，可以开始翻译', 'success');
                startBtn.disabled = false;
            } else {
                updateStatus(data.message || '系统初始化中...', 'info');
            }
        });

        socket.on('translation_status', function(data) {
            if (data.status === 'success') {
                updateStatus(data.message, 'success');
            } else {
                updateStatus(data.message, 'error');
            }
        });

        socket.on('audio_level', function(data) {
            const level = Math.min(data.level * 100, 100);
            audioLevelBar.style.width = level + '%';
        });

        socket.on('translation_result', function(data) {
            addTranslation(data.text, data.latency);
            translationCount++;
            totalLatency += data.latency;
            
            translationCountEl.textContent = translationCount;
            avgLatencyEl.textContent = (totalLatency / translationCount).toFixed(1) + 's';
        });

        // 功能函数
        function startTranslation() {
            socket.emit('start_translation');
            isTranslating = true;
            startBtn.disabled = true;
            stopBtn.disabled = false;
            updateStatus('正在启动翻译...', 'info');
            
            // 清空之前的翻译
            translations.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">正在监听语音...</div>';
        }

        function stopTranslation() {
            socket.emit('stop_translation');
            isTranslating = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            updateStatus('翻译已停止', 'info');
            audioLevelBar.style.width = '0%';
        }

        function updateStatus(message, type = 'info') {
            statusText.textContent = message;
            statusText.className = 'status-text ' + type;
        }

        function addTranslation(text, latency) {
            const item = document.createElement('div');
            item.className = 'translation-item';
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            
            item.innerHTML = `
                <div class="translation-text">${text}</div>
                <div class="translation-meta">
                    <span>时间: ${timeStr}</span>
                    <span>延迟: ${latency.toFixed(1)}s</span>
                </div>
            `;
            
            // 如果是第一个翻译，清空提示文字
            if (translations.children.length === 1 && translations.children[0].style.textAlign === 'center') {
                translations.innerHTML = '';
            }
            
            translations.insertBefore(item, translations.firstChild);
            
            // 限制显示的翻译数量
            while (translations.children.length > 10) {
                translations.removeChild(translations.lastChild);
            }
        }

        // 页面加载完成后请求状态
        window.addEventListener('load', function() {
            socket.emit('get_status');
        });
    </script>
</body>
</html>
