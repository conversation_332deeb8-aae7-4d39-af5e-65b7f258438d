"""
硬件兼容性检查脚本
"""
import sys
import subprocess
import platform
import psutil
import torch

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - 兼容")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - 需要Python 3.8+")
        return False

def check_gpu():
    """检查GPU"""
    print("\n🎮 检查GPU...")
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✅ 检测到 {gpu_count} 个CUDA设备")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            if gpu_memory >= 12:
                print(f"   ✅ GPU {i} 内存充足 (推荐12GB+)")
            else:
                print(f"   ⚠️  GPU {i} 内存可能不足 (当前{gpu_memory:.1f}GB, 推荐12GB+)")
        return True
    else:
        print("❌ 未检测到CUDA GPU")
        print("   建议: 安装CUDA驱动或使用CPU模式(性能较低)")
        return False

def check_memory():
    """检查系统内存"""
    print("\n💾 检查系统内存...")
    memory = psutil.virtual_memory()
    total_gb = memory.total / 1024**3
    available_gb = memory.available / 1024**3
    
    print(f"   总内存: {total_gb:.1f}GB")
    print(f"   可用内存: {available_gb:.1f}GB")
    
    if total_gb >= 16:
        print("✅ 内存充足 (推荐16GB+)")
        return True
    else:
        print("⚠️  内存可能不足 (推荐16GB+)")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("\n💿 检查磁盘空间...")
    disk = psutil.disk_usage('.')
    free_gb = disk.free / 1024**3
    
    print(f"   可用空间: {free_gb:.1f}GB")
    
    if free_gb >= 20:
        print("✅ 磁盘空间充足 (需要20GB+)")
        return True
    else:
        print("❌ 磁盘空间不足 (需要20GB+)")
        return False

def check_audio_devices():
    """检查音频设备"""
    print("\n🎤 检查音频设备...")
    try:
        import sounddevice as sd
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]
        
        if input_devices:
            print(f"✅ 检测到 {len(input_devices)} 个输入设备")
            for i, device in enumerate(input_devices[:3]):  # 显示前3个
                print(f"   {i}: {device['name']}")
            return True
        else:
            print("❌ 未检测到音频输入设备")
            return False
    except ImportError:
        print("⚠️  sounddevice未安装，跳过音频设备检查")
        return True

def check_network():
    """检查网络连接"""
    print("\n🌐 检查网络连接...")
    try:
        import requests
        response = requests.get("https://huggingface.co", timeout=10)
        if response.status_code == 200:
            print("✅ 网络连接正常 (可访问HuggingFace)")
            return True
        else:
            print("⚠️  网络连接异常")
            return False
    except Exception as e:
        print(f"⚠️  网络检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 实时翻译MVP - 硬件兼容性检查")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_gpu(),
        check_memory(),
        check_disk_space(),
        check_audio_devices(),
        check_network(),
    ]
    
    passed = sum(checks)
    total = len(checks)
    
    print("\n" + "=" * 50)
    print(f"📊 检查结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有检查通过! 可以开始安装")
    elif passed >= 4:
        print("⚠️  大部分检查通过，可以尝试安装")
    else:
        print("❌ 多项检查失败，建议先解决硬件问题")
    
    print("\n💡 下一步:")
    print("   1. 运行: python setup.py")
    print("   2. 启动: python app.py")

if __name__ == "__main__":
    main()
