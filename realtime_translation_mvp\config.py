"""
配置文件 - 实时翻译MVP
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()

# 模型配置
MODEL_CONFIG = {
    'name': 'facebook/seamless-m4t-v2-large',
    'local_path': PROJECT_ROOT / 'models' / 'seamless-m4t-v2',
    'cache_dir': PROJECT_ROOT / 'models' / 'cache',
    'device': 'cuda',  # 'cuda' or 'cpu'
    'torch_dtype': 'float16',  # 'float16' or 'float32'
}

# 音频配置
AUDIO_CONFIG = {
    'sample_rate': 16000,
    'channels': 1,
    'chunk_size': 1024,
    'format': 'int16',
    'device_id': None,  # None为默认设备
    'buffer_duration': 0.5,  # 秒
}

# VAD配置
VAD_CONFIG = {
    'threshold': 0.5,
    'min_speech_duration': 0.3,  # 最小语音持续时间(秒)
    'min_silence_duration': 0.5,  # 最小静音持续时间(秒)
    'window_size': 512,
    'hop_length': 160,
}

# 翻译配置
TRANSLATION_CONFIG = {
    'source_lang': 'eng',  # 英文
    'target_lang': 'cmn',  # 中文
    'max_length': 512,
    'num_beams': 1,
    'do_sample': False,
    'batch_size': 1,
}

# Web服务配置
WEB_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True,
    'threaded': True,
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': PROJECT_ROOT / 'logs' / 'app.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_concurrent_requests': 5,
    'request_timeout': 30,  # 秒
    'model_warmup': True,
    'gpu_memory_fraction': 0.8,
}

# 创建必要目录
def create_directories():
    """创建项目所需的目录"""
    directories = [
        PROJECT_ROOT / 'models',
        PROJECT_ROOT / 'models' / 'cache',
        PROJECT_ROOT / 'logs',
        PROJECT_ROOT / 'static' / 'css',
        PROJECT_ROOT / 'static' / 'js',
        PROJECT_ROOT / 'templates',
        PROJECT_ROOT / 'utils',
        PROJECT_ROOT / 'tests',
        PROJECT_ROOT / 'docs',
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
if __name__ == '__main__':
    create_directories()
    print("项目目录结构创建完成!")
