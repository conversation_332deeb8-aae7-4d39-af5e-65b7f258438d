"""
实时翻译MVP - 主应用程序
"""
import os
import sys
import time
import threading
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
from flask_cors import CORS
import numpy as np

# 导入自定义模块
from config import WEB_CONFIG, LOG_CONFIG, create_directories
from utils.audio_processor import AudioProcessor
from utils.vad_detector import VADDetector
from utils.translator import RealTimeTranslator

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'realtime_translation_secret_key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局组件
translator = None
audio_processor = None
vad_detector = None
is_system_ready = False

class TranslationSystem:
    """翻译系统管理器"""
    
    def __init__(self):
        self.translator = RealTimeTranslator()
        self.vad_detector = VADDetector()
        self.audio_processor = None
        self.is_running = False
        self.translation_count = 0
        
    def initialize(self) -> bool:
        """初始化系统"""
        try:
            print("🚀 初始化翻译系统...")
            
            # 创建目录
            create_directories()
            
            # 加载翻译模型
            if not self.translator.load_model():
                return False
            
            # 启动翻译服务
            self.translator.start_translation_service()
            
            print("✅ 翻译系统初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    def start_recording(self):
        """开始录音和翻译"""
        if self.is_running:
            return {"status": "error", "message": "系统已在运行"}
        
        try:
            # 创建音频处理器
            self.audio_processor = AudioProcessor(callback=self._audio_callback)
            
            # 开始录音
            self.audio_processor.start_recording()
            self.is_running = True
            
            return {"status": "success", "message": "开始实时翻译"}
            
        except Exception as e:
            return {"status": "error", "message": f"启动失败: {e}"}
    
    def stop_recording(self):
        """停止录音和翻译"""
        if not self.is_running:
            return {"status": "error", "message": "系统未运行"}
        
        try:
            if self.audio_processor:
                self.audio_processor.stop_recording()
            
            self.is_running = False
            return {"status": "success", "message": "停止翻译"}
            
        except Exception as e:
            return {"status": "error", "message": f"停止失败: {e}"}
    
    def _audio_callback(self, audio_data: np.ndarray):
        """音频回调处理"""
        try:
            # VAD检测
            has_speech, state = self.vad_detector.process_audio(audio_data)
            
            # 发送音频电平
            audio_level = float(np.sqrt(np.mean(audio_data ** 2)))
            socketio.emit('audio_level', {'level': audio_level})
            
            # 如果检测到语音结束，进行翻译
            if state == "speech_end":
                print("检测到语音结束，开始翻译...")
                self.translator.add_audio_for_translation(audio_data)
                
                # 检查翻译结果
                threading.Thread(target=self._check_translation_result, daemon=True).start()
                
        except Exception as e:
            print(f"音频回调错误: {e}")
    
    def _check_translation_result(self):
        """检查翻译结果"""
        max_wait = 5.0  # 最大等待5秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            result = self.translator.get_translation_result()
            if result:
                self.translation_count += 1
                
                # 发送翻译结果
                socketio.emit('translation_result', {
                    'text': result['text'],
                    'timestamp': result['timestamp'],
                    'latency': result['latency'],
                    'count': self.translation_count
                })
                break
            
            time.sleep(0.1)
    
    def get_status(self):
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'translation_count': self.translation_count,
            'model_info': self.translator.get_model_info() if self.translator else {},
            'audio_devices': self.audio_processor.list_audio_devices() if self.audio_processor else []
        }

# 创建翻译系统实例
translation_system = TranslationSystem()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def api_status():
    """获取系统状态"""
    return jsonify(translation_system.get_status())

@app.route('/api/devices')
def api_devices():
    """获取音频设备列表"""
    try:
        from utils.audio_processor import AudioProcessor
        processor = AudioProcessor()
        devices = processor.list_audio_devices()
        return jsonify({"status": "success", "devices": devices})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print(f"客户端连接: {request.sid}")
    emit('system_status', {
        'ready': is_system_ready,
        'message': '系统已就绪' if is_system_ready else '系统初始化中...'
    })

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开"""
    print(f"客户端断开: {request.sid}")

@socketio.on('start_translation')
def handle_start_translation():
    """开始翻译"""
    result = translation_system.start_recording()
    emit('translation_status', result)

@socketio.on('stop_translation')
def handle_stop_translation():
    """停止翻译"""
    result = translation_system.stop_recording()
    emit('translation_status', result)

@socketio.on('get_status')
def handle_get_status():
    """获取状态"""
    status = translation_system.get_status()
    emit('system_status', status)

def initialize_system():
    """初始化系统"""
    global is_system_ready
    
    print("🔄 正在初始化系统...")
    
    try:
        # 初始化翻译系统
        if translation_system.initialize():
            is_system_ready = True
            print("🎉 系统初始化完成，可以开始使用!")
        else:
            print("❌ 系统初始化失败")
            
    except Exception as e:
        print(f"❌ 系统初始化异常: {e}")

def main():
    """主函数"""
    print("🚀 启动实时翻译MVP")
    print("=" * 50)
    
    # 在后台线程中初始化系统
    init_thread = threading.Thread(target=initialize_system, daemon=True)
    init_thread.start()
    
    # 启动Web服务
    try:
        print(f"🌐 启动Web服务...")
        print(f"   地址: http://{WEB_CONFIG['host']}:{WEB_CONFIG['port']}")
        print(f"   按 Ctrl+C 停止服务")
        
        socketio.run(
            app,
            host=WEB_CONFIG['host'],
            port=WEB_CONFIG['port'],
            debug=WEB_CONFIG['debug'],
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 正在关闭服务...")
        
        # 停止翻译系统
        if translation_system.is_running:
            translation_system.stop_recording()
        
        print("✅ 服务已关闭")
    
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")

if __name__ == '__main__':
    main()
