"""
实时翻译模块
"""
import torch
import numpy as np
from transformers import SeamlessM4Tv2Model, AutoProcessor
from typing import Optional, Dict, Any
import threading
import queue
import time
from pathlib import Path
from config import MODEL_CONFIG, TRANSLATION_CONFIG

class RealTimeTranslator:
    """实时翻译器"""
    
    def __init__(self):
        self.model = None
        self.processor = None
        self.device = None
        self.is_loaded = False
        
        # 翻译队列
        self.translation_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.is_translating = False
        
        # 配置
        self.source_lang = TRANSLATION_CONFIG['source_lang']
        self.target_lang = TRANSLATION_CONFIG['target_lang']
        self.max_length = TRANSLATION_CONFIG['max_length']
        
    def load_model(self) -> bool:
        """加载翻译模型"""
        try:
            print("🤖 加载翻译模型...")
            
            # 确定设备
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"使用设备: {self.device}")
            
            # 模型路径
            model_path = MODEL_CONFIG['local_path']
            
            if model_path.exists():
                # 从本地加载
                print("从本地加载模型...")
                model_name = str(model_path)
            else:
                # 从HuggingFace加载
                print("从HuggingFace加载模型...")
                model_name = MODEL_CONFIG['name']
            
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(model_name)
            
            # 加载模型
            torch_dtype = getattr(torch, MODEL_CONFIG['torch_dtype'])
            self.model = SeamlessM4Tv2Model.from_pretrained(
                model_name,
                torch_dtype=torch_dtype,
                device_map="auto" if self.device == "cuda" else None
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            # 设置为评估模式
            self.model.eval()
            
            # 预热模型
            self._warmup_model()
            
            self.is_loaded = True
            print("✅ 模型加载完成")
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def _warmup_model(self):
        """预热模型"""
        try:
            print("🔥 预热模型...")
            
            # 创建虚拟音频数据
            dummy_audio = np.random.randn(16000).astype(np.float32)  # 1秒音频
            
            # 预热推理
            with torch.no_grad():
                inputs = self.processor(
                    audios=dummy_audio,
                    sampling_rate=16000,
                    return_tensors="pt"
                ).to(self.device)
                
                _ = self.model.generate(
                    **inputs,
                    tgt_lang=self.target_lang,
                    max_length=50
                )
            
            print("✅ 模型预热完成")
            
        except Exception as e:
            print(f"⚠️  模型预热失败: {e}")
    
    def translate_audio(self, audio_data: np.ndarray) -> Optional[str]:
        """翻译音频数据"""
        if not self.is_loaded:
            print("模型未加载")
            return None
        
        try:
            # 预处理音频
            if len(audio_data) == 0:
                return None
            
            # 确保音频长度合适
            min_length = 16000 * 0.5  # 最少0.5秒
            if len(audio_data) < min_length:
                # 填充零
                audio_data = np.pad(audio_data, (0, int(min_length - len(audio_data))))
            
            # 归一化
            if np.max(np.abs(audio_data)) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            # 转换为float32
            audio_data = audio_data.astype(np.float32)
            
            # 处理输入
            inputs = self.processor(
                audios=audio_data,
                sampling_rate=16000,
                return_tensors="pt"
            ).to(self.device)
            
            # 生成翻译
            with torch.no_grad():
                generated_tokens = self.model.generate(
                    **inputs,
                    tgt_lang=self.target_lang,
                    max_length=self.max_length,
                    num_beams=TRANSLATION_CONFIG['num_beams'],
                    do_sample=TRANSLATION_CONFIG['do_sample']
                )
            
            # 解码结果
            translated_text = self.processor.decode(
                generated_tokens[0], 
                skip_special_tokens=True
            )
            
            return translated_text.strip()
            
        except Exception as e:
            print(f"翻译错误: {e}")
            return None
    
    def start_translation_service(self):
        """启动翻译服务"""
        if self.is_translating:
            return
        
        self.is_translating = True
        threading.Thread(target=self._translation_worker, daemon=True).start()
        print("翻译服务已启动")
    
    def stop_translation_service(self):
        """停止翻译服务"""
        self.is_translating = False
        print("翻译服务已停止")
    
    def _translation_worker(self):
        """翻译工作线程"""
        while self.is_translating:
            try:
                # 获取音频数据
                audio_data = self.translation_queue.get(timeout=0.1)
                
                # 执行翻译
                start_time = time.time()
                result = self.translate_audio(audio_data)
                end_time = time.time()
                
                if result:
                    # 添加到结果队列
                    self.result_queue.put({
                        'text': result,
                        'timestamp': time.time(),
                        'latency': end_time - start_time
                    })
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"翻译工作线程错误: {e}")
    
    def add_audio_for_translation(self, audio_data: np.ndarray):
        """添加音频到翻译队列"""
        try:
            self.translation_queue.put_nowait(audio_data)
        except queue.Full:
            print("翻译队列已满")
    
    def get_translation_result(self) -> Optional[Dict[str, Any]]:
        """获取翻译结果"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'loaded': self.is_loaded,
            'device': self.device,
            'source_lang': self.source_lang,
            'target_lang': self.target_lang,
            'model_name': MODEL_CONFIG['name']
        }

class BatchTranslator:
    """批量翻译器"""
    
    def __init__(self, translator: RealTimeTranslator):
        self.translator = translator
    
    def translate_file(self, audio_file: str) -> Optional[str]:
        """翻译音频文件"""
        try:
            from utils.audio_processor import AudioFileProcessor
            
            # 加载音频
            audio_data = AudioFileProcessor.load_audio(audio_file)
            
            if len(audio_data) == 0:
                return None
            
            # 翻译
            return self.translator.translate_audio(audio_data)
            
        except Exception as e:
            print(f"文件翻译错误: {e}")
            return None
    
    def translate_batch(self, audio_files: list) -> Dict[str, str]:
        """批量翻译音频文件"""
        results = {}
        
        for file_path in audio_files:
            print(f"翻译文件: {file_path}")
            result = self.translate_file(file_path)
            results[file_path] = result or "翻译失败"
        
        return results

# 测试代码
if __name__ == "__main__":
    translator = RealTimeTranslator()
    
    # 加载模型
    if translator.load_model():
        print("模型信息:", translator.get_model_info())
        
        # 测试翻译
        print("生成测试音频...")
        test_audio = np.random.randn(16000 * 2).astype(np.float32)  # 2秒音频
        
        print("执行翻译...")
        result = translator.translate_audio(test_audio)
        print(f"翻译结果: {result}")
    else:
        print("模型加载失败")
