"""
环境配置和模型下载脚本
"""
import os
import sys
import subprocess
import torch
from pathlib import Path
from config import create_directories, MODEL_CONFIG, PROJECT_ROOT

def install_requirements():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def install_pytorch_gpu():
    """安装GPU版本的PyTorch"""
    print("🔥 检查PyTorch GPU支持...")
    
    if torch.cuda.is_available():
        print("✅ PyTorch GPU支持已就绪")
        return True
    
    print("⚠️  安装GPU版本PyTorch...")
    try:
        # 安装CUDA版本的PyTorch
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "torch", "torchaudio", "--index-url", 
            "https://download.pytorch.org/whl/cu118"
        ])
        print("✅ PyTorch GPU版本安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  GPU版本安装失败，将使用CPU版本: {e}")
        return False

def download_model():
    """下载SeamlessM4T模型"""
    print("🤖 下载SeamlessM4T模型...")
    
    try:
        from transformers import SeamlessM4Tv2Model, AutoProcessor
        
        model_path = MODEL_CONFIG['local_path']
        
        # 检查模型是否已存在
        if model_path.exists() and any(model_path.iterdir()):
            print("✅ 模型已存在，跳过下载")
            return True
        
        print("📥 正在下载模型 (约8GB，请耐心等待)...")
        
        # 下载模型和处理器
        model = SeamlessM4Tv2Model.from_pretrained(
            MODEL_CONFIG['name'],
            cache_dir=MODEL_CONFIG['cache_dir'],
            torch_dtype=getattr(torch, MODEL_CONFIG['torch_dtype'])
        )
        
        processor = AutoProcessor.from_pretrained(
            MODEL_CONFIG['name'],
            cache_dir=MODEL_CONFIG['cache_dir']
        )
        
        # 保存到本地
        model_path.mkdir(parents=True, exist_ok=True)
        model.save_pretrained(model_path)
        processor.save_pretrained(model_path)
        
        print("✅ 模型下载并保存完成")
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 确保有足够磁盘空间 (>20GB)")
        print("   3. 尝试使用VPN或镜像源")
        return False

def test_model():
    """测试模型加载"""
    print("🧪 测试模型加载...")
    
    try:
        from transformers import SeamlessM4Tv2Model, AutoProcessor
        import torch
        
        model_path = MODEL_CONFIG['local_path']
        
        if not model_path.exists():
            print("❌ 模型文件不存在")
            return False
        
        # 加载模型
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        model = SeamlessM4Tv2Model.from_pretrained(
            model_path,
            torch_dtype=getattr(torch, MODEL_CONFIG['torch_dtype'])
        ).to(device)
        
        processor = AutoProcessor.from_pretrained(model_path)
        
        print(f"✅ 模型加载成功 (设备: {device})")
        
        # 清理内存
        del model
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # Windows批处理文件
    bat_content = """@echo off
echo 启动实时翻译MVP...
python app.py
pause
"""
    
    with open(PROJECT_ROOT / "start.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # Linux/Mac shell脚本
    sh_content = """#!/bin/bash
echo "启动实时翻译MVP..."
python app.py
"""
    
    with open(PROJECT_ROOT / "start.sh", "w", encoding="utf-8") as f:
        f.write(sh_content)
    
    # 给shell脚本执行权限
    try:
        os.chmod(PROJECT_ROOT / "start.sh", 0o755)
    except:
        pass
    
    print("✅ 启动脚本创建完成")

def main():
    """主安装函数"""
    print("🚀 实时翻译MVP - 环境配置")
    print("=" * 50)
    
    # 创建目录结构
    print("📁 创建项目目录...")
    create_directories()
    print("✅ 目录结构创建完成")
    
    # 安装步骤
    steps = [
        ("安装依赖包", install_requirements),
        ("配置PyTorch GPU", install_pytorch_gpu),
        ("下载模型", download_model),
        ("测试模型", test_model),
        ("创建启动脚本", create_startup_script),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if step_func():
            success_count += 1
        else:
            print(f"⚠️  {step_name} 失败，但继续执行...")
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count >= 4:
        print("🎉 安装基本完成!")
        print("\n💡 下一步:")
        print("   1. 运行: python app.py")
        print("   2. 或双击: start.bat (Windows)")
        print("   3. 访问: http://localhost:5000")
    else:
        print("❌ 安装遇到问题，请检查错误信息")

if __name__ == "__main__":
    main()
