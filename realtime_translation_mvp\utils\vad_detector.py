"""
语音活动检测 (Voice Activity Detection)
"""
import numpy as np
import torch
import librosa
from typing import List, <PERSON>ple
import webrtcvad
from config import VAD_CONFIG, AUDIO_CONFIG

class VADDetector:
    """语音活动检测器"""
    
    def __init__(self):
        self.vad = webrtcvad.Vad(2)  # 敏感度: 0-3, 2为中等
        self.sample_rate = AUDIO_CONFIG['sample_rate']
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
        # 状态跟踪
        self.speech_frames = []
        self.silence_frames = []
        self.is_speaking = False
        
        # 配置参数
        self.min_speech_frames = int(VAD_CONFIG['min_speech_duration'] * 1000 / self.frame_duration)
        self.min_silence_frames = int(VAD_CONFIG['min_silence_duration'] * 1000 / self.frame_duration)
        
    def preprocess_audio(self, audio_data: np.ndarray) -> bytes:
        """预处理音频数据"""
        # 确保音频是16kHz, 16bit, 单声道
        if len(audio_data.shape) > 1:
            audio_data = audio_data.mean(axis=1)
        
        # 重采样到16kHz
        if self.sample_rate != 16000:
            audio_data = librosa.resample(audio_data, orig_sr=self.sample_rate, target_sr=16000)
        
        # 转换为16bit整数
        audio_data = (audio_data * 32767).astype(np.int16)
        
        return audio_data.tobytes()
    
    def detect_speech(self, audio_chunk: np.ndarray) -> bool:
        """检测音频块中是否包含语音"""
        try:
            # 预处理音频
            audio_bytes = self.preprocess_audio(audio_chunk)
            
            # 分帧检测
            speech_count = 0
            total_frames = 0
            
            for i in range(0, len(audio_bytes) - self.frame_size * 2, self.frame_size * 2):
                frame = audio_bytes[i:i + self.frame_size * 2]
                if len(frame) == self.frame_size * 2:
                    is_speech = self.vad.is_speech(frame, 16000)
                    if is_speech:
                        speech_count += 1
                    total_frames += 1
            
            # 计算语音比例
            if total_frames > 0:
                speech_ratio = speech_count / total_frames
                return speech_ratio > VAD_CONFIG['threshold']
            
            return False
            
        except Exception as e:
            print(f"VAD检测错误: {e}")
            return False
    
    def update_state(self, has_speech: bool) -> str:
        """更新语音状态"""
        if has_speech:
            self.speech_frames.append(True)
            self.silence_frames = []
            
            # 检查是否开始说话
            if not self.is_speaking and len(self.speech_frames) >= self.min_speech_frames:
                self.is_speaking = True
                return "speech_start"
                
        else:
            self.silence_frames.append(True)
            self.speech_frames = []
            
            # 检查是否停止说话
            if self.is_speaking and len(self.silence_frames) >= self.min_silence_frames:
                self.is_speaking = False
                return "speech_end"
        
        if self.is_speaking:
            return "speech_continue"
        else:
            return "silence"
    
    def process_audio(self, audio_chunk: np.ndarray) -> Tuple[bool, str]:
        """处理音频块并返回检测结果"""
        has_speech = self.detect_speech(audio_chunk)
        state = self.update_state(has_speech)
        
        return has_speech, state
    
    def reset(self):
        """重置检测器状态"""
        self.speech_frames = []
        self.silence_frames = []
        self.is_speaking = False

class EnergyVAD:
    """基于能量的简单VAD"""
    
    def __init__(self, threshold: float = 0.01):
        self.threshold = threshold
        self.window_size = VAD_CONFIG['window_size']
        self.hop_length = VAD_CONFIG['hop_length']
    
    def detect_speech(self, audio_chunk: np.ndarray) -> bool:
        """基于能量检测语音"""
        # 计算短时能量
        energy = np.sum(audio_chunk ** 2) / len(audio_chunk)
        return energy > self.threshold
    
    def get_speech_segments(self, audio: np.ndarray) -> List[Tuple[int, int]]:
        """获取语音段的起止位置"""
        segments = []
        
        # 滑动窗口计算能量
        energies = []
        for i in range(0, len(audio) - self.window_size, self.hop_length):
            window = audio[i:i + self.window_size]
            energy = np.sum(window ** 2) / len(window)
            energies.append(energy > self.threshold)
        
        # 找到连续的语音段
        start = None
        for i, is_speech in enumerate(energies):
            if is_speech and start is None:
                start = i * self.hop_length
            elif not is_speech and start is not None:
                end = i * self.hop_length
                segments.append((start, end))
                start = None
        
        # 处理最后一个段
        if start is not None:
            segments.append((start, len(audio)))
        
        return segments

def create_vad_detector(method: str = "webrtc") -> VADDetector:
    """创建VAD检测器"""
    if method == "webrtc":
        return VADDetector()
    elif method == "energy":
        return EnergyVAD()
    else:
        raise ValueError(f"不支持的VAD方法: {method}")

# 测试代码
if __name__ == "__main__":
    import sounddevice as sd
    
    print("测试VAD检测器...")
    vad = VADDetector()
    
    def audio_callback(indata, frames, time, status):
        audio_chunk = indata[:, 0]  # 单声道
        has_speech, state = vad.process_audio(audio_chunk)
        
        if state in ["speech_start", "speech_end"]:
            print(f"状态变化: {state}")
    
    print("开始录音测试 (按Ctrl+C停止)...")
    try:
        with sd.InputStream(callback=audio_callback, 
                          channels=1, 
                          samplerate=AUDIO_CONFIG['sample_rate']):
            input("按Enter停止...")
    except KeyboardInterrupt:
        print("测试结束")
