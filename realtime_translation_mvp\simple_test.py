"""
简单测试应用 - 验证基本功能
"""
import os
import sys

# 设置环境变量
os.environ['HF_HOME'] = 'E:/seamless_cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:/seamless_cache'
os.environ['TORCH_HOME'] = 'E:/seamless_cache'

print("🚀 启动简单测试应用...")
print("=" * 50)

# 检查基本依赖
print("1️⃣ 检查基本依赖...")
try:
    import torch
    print(f"✅ PyTorch {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("⚠️  使用CPU模式")
        
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")
    sys.exit(1)

try:
    import transformers
    print(f"✅ Transformers {transformers.__version__}")
except ImportError as e:
    print(f"❌ Transformers导入失败: {e}")
    sys.exit(1)

try:
    from flask import Flask
    from flask_socketio import SocketIO
    print("✅ Flask和SocketIO")
except ImportError as e:
    print(f"❌ Flask导入失败: {e}")
    sys.exit(1)

# 检查模型缓存
print("\n2️⃣ 检查模型缓存...")
cache_dir = "E:/seamless_cache"
if os.path.exists(cache_dir):
    print(f"✅ 缓存目录存在: {cache_dir}")
    
    # 查找模型文件
    model_files = []
    for root, dirs, files in os.walk(cache_dir):
        for file in files:
            if file.endswith(('.safetensors', '.bin', '.json')):
                model_files.append(file)
    
    print(f"✅ 找到模型文件: {len(model_files)} 个")
else:
    print(f"❌ 缓存目录不存在: {cache_dir}")

# 创建简单的Flask应用
print("\n3️⃣ 创建Web应用...")

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

@app.route('/')
def index():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>实时翻译测试</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px; }
            button:hover { background: #0056b3; }
            button:disabled { background: #6c757d; cursor: not-allowed; }
            #log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎤 实时翻译MVP - 测试版</h1>
            
            <div id="status" class="status info">
                系统初始化中...
            </div>
            
            <div style="text-align: center;">
                <button onclick="testConnection()">测试连接</button>
                <button onclick="testModel()" id="modelBtn" disabled>测试模型</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <h3>系统日志</h3>
            <div id="log"></div>
        </div>

        <script>
            const socket = io();
            const statusDiv = document.getElementById('status');
            const logDiv = document.getElementById('log');
            const modelBtn = document.getElementById('modelBtn');
            
            function addLog(message, type = 'info') {
                const time = new Date().toLocaleTimeString();
                const logEntry = `[${time}] ${message}\\n`;
                logDiv.textContent += logEntry;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            
            function updateStatus(message, type = 'info') {
                statusDiv.textContent = message;
                statusDiv.className = `status ${type}`;
            }
            
            socket.on('connect', function() {
                addLog('✅ 已连接到服务器', 'success');
                updateStatus('已连接到服务器，系统就绪', 'success');
                modelBtn.disabled = false;
            });
            
            socket.on('disconnect', function() {
                addLog('❌ 与服务器断开连接', 'error');
                updateStatus('与服务器断开连接', 'error');
                modelBtn.disabled = true;
            });
            
            socket.on('test_result', function(data) {
                addLog(`测试结果: ${data.message}`, data.success ? 'success' : 'error');
                updateStatus(data.message, data.success ? 'success' : 'error');
            });
            
            function testConnection() {
                addLog('🔄 测试连接...');
                socket.emit('test_connection');
            }
            
            function testModel() {
                addLog('🔄 测试模型加载...');
                updateStatus('正在测试模型...', 'info');
                socket.emit('test_model');
            }
            
            function clearLog() {
                logDiv.textContent = '';
            }
            
            // 页面加载完成
            window.onload = function() {
                addLog('🚀 页面加载完成');
                addLog('💡 点击"测试连接"开始测试');
            };
        </script>
    </body>
    </html>
    '''

@socketio.on('connect')
def handle_connect():
    print(f"✅ 客户端连接: {socketio.request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    print(f"❌ 客户端断开: {socketio.request.sid}")

@socketio.on('test_connection')
def handle_test_connection():
    print("🔄 测试连接...")
    socketio.emit('test_result', {
        'success': True,
        'message': '连接测试成功！'
    })

@socketio.on('test_model')
def handle_test_model():
    print("🔄 测试模型...")
    try:
        # 简单的模型测试
        from transformers import AutoTokenizer
        
        # 尝试加载tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            "facebook/seamless-m4t-v2-large",
            cache_dir="E:/seamless_cache"
        )
        
        socketio.emit('test_result', {
            'success': True,
            'message': '模型tokenizer加载成功！'
        })
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        socketio.emit('test_result', {
            'success': False,
            'message': f'模型测试失败: {str(e)[:100]}...'
        })

if __name__ == '__main__':
    print("\n4️⃣ 启动Web服务...")
    print("🌐 访问地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
