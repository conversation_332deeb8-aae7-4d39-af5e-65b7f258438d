"""
快速启动脚本 - 使用现有模型缓存
"""
import os
import sys
import subprocess
from pathlib import Path

# 设置环境变量
os.environ['HF_HOME'] = 'E:/seamless_cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:/seamless_cache'
os.environ['TORCH_HOME'] = 'E:/seamless_cache'

def check_requirements():
    """检查必要的依赖"""
    required_packages = [
        'torch',
        'transformers',
        'flask',
        'flask-socketio',
        'sounddevice',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    return missing_packages

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"\n📦 安装缺失的包: {', '.join(packages)}")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install"
        ] + packages)
        print("✅ 包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 包安装失败: {e}")
        return False

def test_model_access():
    """测试模型访问"""
    print("\n🤖 测试模型访问...")
    
    try:
        from transformers import SeamlessM4Tv2Model, AutoProcessor
        import torch
        
        # 检查缓存目录
        cache_dir = Path('E:/seamless_cache')
        if not cache_dir.exists():
            print("❌ 模型缓存目录不存在")
            return False
        
        # 检查模型文件
        model_files = list(cache_dir.rglob("*.safetensors"))
        if not model_files:
            print("❌ 未找到模型文件")
            return False
        
        print(f"✅ 找到模型文件: {len(model_files)} 个")
        
        # 测试模型加载
        print("🔄 测试模型加载...")
        
        # 使用较小的内存占用进行测试
        processor = AutoProcessor.from_pretrained(
            "facebook/seamless-m4t-v2-large",
            cache_dir=cache_dir
        )
        print("✅ 处理器加载成功")
        
        # 检查GPU
        if torch.cuda.is_available():
            print(f"✅ GPU可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  使用CPU模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def create_minimal_config():
    """创建最小配置文件"""
    config_content = f'''"""
最小配置文件
"""
import os
from pathlib import Path

# 设置缓存目录
os.environ['HF_HOME'] = 'E:/seamless_cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:/seamless_cache'
os.environ['TORCH_HOME'] = 'E:/seamless_cache'

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.absolute()

# 模型配置
MODEL_CONFIG = {{
    'name': 'facebook/seamless-m4t-v2-large',
    'cache_dir': Path('E:/seamless_cache'),
    'device': 'cuda',
    'torch_dtype': 'float16',
}}

# 音频配置
AUDIO_CONFIG = {{
    'sample_rate': 16000,
    'channels': 1,
    'chunk_size': 1024,
}}

# Web配置
WEB_CONFIG = {{
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True,
}}
'''
    
    with open('minimal_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 配置文件已创建")

def create_minimal_app():
    """创建最小应用"""
    app_content = '''"""
最小实时翻译应用
"""
import os
import sys
import time
import threading
from pathlib import Path

# 设置环境
os.environ['HF_HOME'] = 'E:/seamless_cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:/seamless_cache'

from flask import Flask, render_template_string
from flask_socketio import SocketIO, emit
import torch
import numpy as np

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
translator = None
is_ready = False

class SimpleTranslator:
    def __init__(self):
        self.model = None
        self.processor = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
    def load_model(self):
        try:
            from transformers import SeamlessM4Tv2Model, AutoProcessor
            
            print("🔄 加载模型...")
            self.processor = AutoProcessor.from_pretrained(
                "facebook/seamless-m4t-v2-large",
                cache_dir="E:/seamless_cache"
            )
            
            self.model = SeamlessM4Tv2Model.from_pretrained(
                "facebook/seamless-m4t-v2-large",
                cache_dir="E:/seamless_cache",
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            ).to(self.device)
            
            print("✅ 模型加载完成")
            return True
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def translate_text(self, text):
        # 简单的文本翻译示例
        return f"[翻译] {text}"

@app.route('/')
def index():
    return render_template_string("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>实时翻译MVP</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    </head>
    <body>
        <h1>🎤 实时翻译MVP</h1>
        <div id="status">初始化中...</div>
        <button onclick="testTranslation()">测试翻译</button>
        <div id="result"></div>
        
        <script>
            const socket = io();
            
            socket.on('status', function(data) {
                document.getElementById('status').textContent = data.message;
            });
            
            socket.on('translation', function(data) {
                document.getElementById('result').innerHTML += '<p>' + data.text + '</p>';
            });
            
            function testTranslation() {
                socket.emit('test_translate', {text: 'Hello World'});
            }
        </script>
    </body>
    </html>
    """)

@socketio.on('connect')
def handle_connect():
    emit('status', {'message': '已连接' if is_ready else '模型加载中...'})

@socketio.on('test_translate')
def handle_test_translate(data):
    if translator:
        result = translator.translate_text(data['text'])
        emit('translation', {'text': result})

def init_system():
    global translator, is_ready
    
    translator = SimpleTranslator()
    if translator.load_model():
        is_ready = True
        print("🎉 系统就绪!")
    else:
        print("❌ 系统初始化失败")

if __name__ == '__main__':
    print("🚀 启动最小MVP...")
    
    # 后台初始化
    threading.Thread(target=init_system, daemon=True).start()
    
    # 启动服务
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
'''
    
    with open('minimal_app.py', 'w', encoding='utf-8') as f:
        f.write(app_content)
    
    print("✅ 最小应用已创建")

def main():
    """主函数"""
    print("🚀 实时翻译MVP - 快速启动")
    print("=" * 50)
    
    # 1. 检查依赖
    print("1️⃣ 检查依赖包...")
    missing = check_requirements()
    
    if missing:
        print(f"\\n缺少依赖: {missing}")
        if input("是否自动安装? (y/n): ").lower() == 'y':
            if not install_missing_packages(missing):
                print("❌ 依赖安装失败，请手动安装")
                return
        else:
            print("请手动安装缺失的依赖包")
            return
    
    # 2. 测试模型
    print("\\n2️⃣ 测试模型访问...")
    if not test_model_access():
        print("❌ 模型访问失败")
        return
    
    # 3. 创建文件
    print("\\n3️⃣ 创建应用文件...")
    create_minimal_config()
    create_minimal_app()
    
    # 4. 启动选项
    print("\\n4️⃣ 启动选项:")
    print("   A. 运行最小应用: python minimal_app.py")
    print("   B. 运行完整应用: python app.py")
    
    choice = input("\\n选择启动方式 (A/B): ").upper()
    
    if choice == 'A':
        print("\\n🚀 启动最小应用...")
        subprocess.run([sys.executable, "minimal_app.py"])
    elif choice == 'B':
        print("\\n🚀 启动完整应用...")
        subprocess.run([sys.executable, "app.py"])
    else:
        print("\\n💡 手动启动:")
        print("   python minimal_app.py  # 最小版本")
        print("   python app.py          # 完整版本")

if __name__ == "__main__":
    main()
