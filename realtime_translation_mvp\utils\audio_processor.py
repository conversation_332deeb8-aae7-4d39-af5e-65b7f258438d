"""
音频处理模块
"""
import numpy as np
import sounddevice as sd
import threading
import queue
import time
from typing import Callable, Optional
import librosa
from config import AUDIO_CONFIG

class AudioProcessor:
    """实时音频处理器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.callback = callback
        self.is_recording = False
        self.audio_queue = queue.Queue(maxsize=100)
        self.recording_thread = None
        
        # 音频配置
        self.sample_rate = AUDIO_CONFIG['sample_rate']
        self.channels = AUDIO_CONFIG['channels']
        self.chunk_size = AUDIO_CONFIG['chunk_size']
        self.device_id = AUDIO_CONFIG['device_id']
        
        # 缓冲区
        self.buffer = np.array([])
        self.buffer_duration = AUDIO_CONFIG['buffer_duration']
        self.buffer_size = int(self.sample_rate * self.buffer_duration)
        
    def list_audio_devices(self):
        """列出可用的音频设备"""
        devices = sd.query_devices()
        input_devices = []
        
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                input_devices.append({
                    'id': i,
                    'name': device['name'],
                    'channels': device['max_input_channels'],
                    'sample_rate': device['default_samplerate']
                })
        
        return input_devices
    
    def set_device(self, device_id: int):
        """设置音频设备"""
        self.device_id = device_id
        
    def _audio_callback(self, indata, frames, time, status):
        """音频回调函数"""
        if status:
            print(f"音频状态: {status}")
        
        if self.is_recording:
            # 转换为单声道
            if indata.shape[1] > 1:
                audio_data = indata.mean(axis=1)
            else:
                audio_data = indata[:, 0]
            
            # 添加到队列
            try:
                self.audio_queue.put_nowait(audio_data.copy())
            except queue.Full:
                print("音频队列已满，丢弃数据")
    
    def _process_audio_thread(self):
        """音频处理线程"""
        while self.is_recording:
            try:
                # 获取音频数据
                audio_chunk = self.audio_queue.get(timeout=0.1)
                
                # 添加到缓冲区
                self.buffer = np.concatenate([self.buffer, audio_chunk])
                
                # 如果缓冲区足够大，处理音频
                if len(self.buffer) >= self.buffer_size:
                    # 取出一个缓冲区大小的数据
                    process_data = self.buffer[:self.buffer_size]
                    self.buffer = self.buffer[self.buffer_size//2:]  # 保留50%重叠
                    
                    # 调用回调函数
                    if self.callback:
                        self.callback(process_data)
                        
            except queue.Empty:
                continue
            except Exception as e:
                print(f"音频处理错误: {e}")
    
    def start_recording(self):
        """开始录音"""
        if self.is_recording:
            print("已在录音中")
            return
        
        print("开始录音...")
        self.is_recording = True
        self.buffer = np.array([])
        
        try:
            # 启动音频流
            self.stream = sd.InputStream(
                callback=self._audio_callback,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                device=self.device_id
            )
            self.stream.start()
            
            # 启动处理线程
            self.recording_thread = threading.Thread(
                target=self._process_audio_thread,
                daemon=True
            )
            self.recording_thread.start()
            
            print("录音已开始")
            
        except Exception as e:
            print(f"录音启动失败: {e}")
            self.is_recording = False
    
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            print("未在录音")
            return
        
        print("停止录音...")
        self.is_recording = False
        
        try:
            if hasattr(self, 'stream'):
                self.stream.stop()
                self.stream.close()
            
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=1.0)
            
            print("录音已停止")
            
        except Exception as e:
            print(f"录音停止错误: {e}")
    
    def get_audio_level(self) -> float:
        """获取当前音频电平"""
        if len(self.buffer) > 0:
            return float(np.sqrt(np.mean(self.buffer ** 2)))
        return 0.0

class AudioFileProcessor:
    """音频文件处理器"""
    
    @staticmethod
    def load_audio(file_path: str, target_sr: int = 16000) -> np.ndarray:
        """加载音频文件"""
        try:
            audio, sr = librosa.load(file_path, sr=target_sr, mono=True)
            return audio
        except Exception as e:
            print(f"音频文件加载失败: {e}")
            return np.array([])
    
    @staticmethod
    def save_audio(audio: np.ndarray, file_path: str, sr: int = 16000):
        """保存音频文件"""
        try:
            import soundfile as sf
            sf.write(file_path, audio, sr)
            print(f"音频已保存: {file_path}")
        except Exception as e:
            print(f"音频保存失败: {e}")
    
    @staticmethod
    def normalize_audio(audio: np.ndarray) -> np.ndarray:
        """音频归一化"""
        if len(audio) == 0:
            return audio
        
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            return audio / max_val
        return audio
    
    @staticmethod
    def remove_silence(audio: np.ndarray, sr: int = 16000, 
                      top_db: int = 20) -> np.ndarray:
        """移除静音段"""
        try:
            # 使用librosa移除静音
            audio_trimmed, _ = librosa.effects.trim(audio, top_db=top_db)
            return audio_trimmed
        except Exception as e:
            print(f"静音移除失败: {e}")
            return audio

# 测试代码
if __name__ == "__main__":
    def test_callback(audio_data):
        level = np.sqrt(np.mean(audio_data ** 2))
        print(f"音频电平: {level:.4f}")
    
    processor = AudioProcessor(callback=test_callback)
    
    print("可用音频设备:")
    devices = processor.list_audio_devices()
    for device in devices:
        print(f"  {device['id']}: {device['name']}")
    
    print("\n开始录音测试 (5秒)...")
    processor.start_recording()
    time.sleep(5)
    processor.stop_recording()
    print("测试完成")
