import plotly.graph_objects as go
import plotly.express as px
import json
import numpy as np

# Data for the system architecture
data = {
  "components": [
    {"name": "Audio Input", "type": "input", "tech": "Microphone"},
    {"name": "Real-time Stream", "type": "process", "tech": "WebSocket/WebRTC"},
    {"name": "Voice Activity Detection", "type": "process", "tech": "VAD"},
    {"name": "Audio Chunking", "type": "process", "tech": "Buffer Management"}, 
    {"name": "Speech Recognition", "type": "process", "tech": "SeamlessM4T v2"},
    {"name": "Translation", "type": "process", "tech": "SeamlessM4T v2"},
    {"name": "Text Output", "type": "output", "tech": "Display"},
    {"name": "Backend Server", "type": "infrastructure", "tech": "GPU Server"},
    {"name": "Frontend Client", "type": "infrastructure", "tech": "Web Browser"}
  ]
}

# Define positions for components in flow layout
positions = {
    "Audio Input": (1, 5),
    "Real-time Stream": (2, 5),
    "Voice Activity Detection": (3, 5),
    "Audio Chunking": (4, 5),
    "Speech Recognition": (5, 4),
    "Translation": (6, 4),
    "Text Output": (7, 5),
    "Backend Server": (5, 2),
    "Frontend Client": (2, 2)
}

# Define connections (flow)
connections = [
    ("Audio Input", "Real-time Stream"),
    ("Real-time Stream", "Voice Activity Detection"),
    ("Voice Activity Detection", "Audio Chunking"),
    ("Audio Chunking", "Speech Recognition"),
    ("Speech Recognition", "Translation"),
    ("Translation", "Text Output"),
    ("Backend Server", "Speech Recognition"),
    ("Backend Server", "Translation"),
    ("Frontend Client", "Real-time Stream"),
    ("Frontend Client", "Text Output")
]

# Color mapping for component types
color_map = {
    "input": "#1FB8CD",
    "process": "#DB4545", 
    "output": "#2E8B57",
    "infrastructure": "#5D878F"
}

# Create figure
fig = go.Figure()

# Add connection lines
for start, end in connections:
    start_pos = positions[start]
    end_pos = positions[end]
    
    fig.add_trace(go.Scatter(
        x=[start_pos[0], end_pos[0]],
        y=[start_pos[1], end_pos[1]],
        mode='lines',
        line=dict(color='gray', width=2),
        showlegend=False,
        hoverinfo='skip'
    ))

# Add arrow markers for flow direction
for start, end in connections:
    start_pos = positions[start]
    end_pos = positions[end]
    
    # Calculate arrow position (70% along the line)
    arrow_x = start_pos[0] + 0.7 * (end_pos[0] - start_pos[0])
    arrow_y = start_pos[1] + 0.7 * (end_pos[1] - start_pos[1])
    
    fig.add_trace(go.Scatter(
        x=[arrow_x],
        y=[arrow_y],
        mode='markers',
        marker=dict(
            symbol='arrow-right',
            size=8,
            color='gray'
        ),
        showlegend=False,
        hoverinfo='skip'
    ))

# Add component boxes with text annotations
legend_added = set()
for comp in data["components"]:
    name = comp["name"]
    comp_type = comp["type"]
    tech = comp["tech"]
    pos = positions[name]
    
    # Determine marker shape based on type
    if comp_type == "process":
        marker_symbol = "square"
        marker_size = 30
    elif comp_type in ["input", "output"]:
        marker_symbol = "circle"
        marker_size = 25
    else:  # infrastructure
        marker_symbol = "diamond"
        marker_size = 35
    
    show_legend = comp_type not in legend_added
    if show_legend:
        legend_added.add(comp_type)
    
    # Add marker for component
    fig.add_trace(go.Scatter(
        x=[pos[0]],
        y=[pos[1]],
        mode='markers',
        marker=dict(
            symbol=marker_symbol,
            size=marker_size,
            color=color_map[comp_type],
            line=dict(width=2, color='white')
        ),
        name=comp_type.title(),
        hovertemplate=f'<b>{name}</b><br>{tech}<extra></extra>',
        showlegend=show_legend
    ))
    
    # Add text labels for each component (shortened to fit requirements)
    short_name = name[:15] if len(name) <= 15 else name.replace(" ", "<br>")
    short_tech = tech[:15] if len(tech) <= 15 else tech.replace("/", "<br>")
    
    fig.add_annotation(
        x=pos[0],
        y=pos[1] - 0.3,
        text=f"<b>{short_name}</b><br><i>{short_tech}</i>",
        showarrow=False,
        font=dict(size=9, color='black'),
        align='center',
        bgcolor='rgba(255,255,255,0.8)',
        bordercolor='gray',
        borderwidth=1
    )

# Update layout
fig.update_layout(
    title="Speech Translation System Architecture",
    xaxis=dict(
        showgrid=False,
        showticklabels=False,
        zeroline=False,
        range=[0, 8]
    ),
    yaxis=dict(
        showgrid=False,
        showticklabels=False,
        zeroline=False,
        range=[1, 6]
    ),
    legend=dict(
        orientation='h',
        yanchor='bottom',
        y=1.05,
        xanchor='center',
        x=0.5
    ),
    plot_bgcolor='white'
)

# Save the chart
fig.write_image("speech_translation_architecture.png")