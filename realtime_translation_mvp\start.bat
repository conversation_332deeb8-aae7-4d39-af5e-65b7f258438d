@echo off
chcp 65001 >nul
title 实时翻译MVP启动器

echo.
echo ========================================
echo    🎤 实时翻译MVP启动器
echo    英文 → 中文 实时翻译
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 🔍 检查模型缓存...
if not exist "E:\seamless_cache" (
    echo ❌ 模型缓存目录不存在: E:\seamless_cache
    echo 请确保SeamlessM4T模型已下载到该目录
    pause
    exit /b 1
)

echo ✅ 模型缓存目录存在
echo.

echo 📦 检查依赖包...
python -c "import torch, transformers, flask, flask_socketio, sounddevice, numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要依赖包
    echo.
    set /p install="是否自动安装依赖包? (y/n): "
    if /i "%install%"=="y" (
        echo 🔄 安装依赖包...
        pip install torch transformers flask flask-socketio sounddevice numpy
        if errorlevel 1 (
            echo ❌ 依赖包安装失败
            pause
            exit /b 1
        )
        echo ✅ 依赖包安装完成
    ) else (
        echo 请手动安装依赖包后重试
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.

echo 🚀 启动选项:
echo    1. 快速启动 (自动检测和配置)
echo    2. 最小版本 (测试用)
echo    3. 完整版本 (全功能)
echo    4. 硬件检查
echo.

set /p choice="请选择启动方式 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动快速版本...
    python quick_start.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动最小版本...
    python minimal_app.py
) else if "%choice%"=="3" (
    echo.
    echo 🚀 启动完整版本...
    python app.py
) else if "%choice%"=="4" (
    echo.
    echo 🔍 执行硬件检查...
    python check_hardware.py
    pause
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    pause
) else (
    echo.
    echo ✅ 程序已退出
)

pause
