# 故障排除指南

## 🔧 常见问题解决方案

### 1. 模型加载问题

#### 问题：模型加载失败
```
❌ 模型加载失败: No such file or directory
```

**解决方案：**
1. 检查模型缓存目录是否存在：
   ```bash
   dir E:\seamless_cache
   ```

2. 如果目录不存在，重新下载模型：
   ```python
   from transformers import SeamlessM4Tv2Model, AutoProcessor
   import os
   
   os.environ['HF_HOME'] = 'E:/seamless_cache'
   model = SeamlessM4Tv2Model.from_pretrained("facebook/seamless-m4t-v2-large")
   ```

3. 检查网络连接和HuggingFace访问

#### 问题：GPU内存不足
```
❌ CUDA out of memory
```

**解决方案：**
1. 关闭其他GPU应用程序
2. 降低模型精度：
   ```python
   # 在config.py中修改
   MODEL_CONFIG['torch_dtype'] = 'float32'  # 改为float32
   ```
3. 使用CPU模式：
   ```python
   MODEL_CONFIG['device'] = 'cpu'
   ```

### 2. 音频设备问题

#### 问题：未检测到麦克风
```
❌ 未检测到音频输入设备
```

**解决方案：**
1. 检查麦克风连接
2. 检查Windows音频设备设置
3. 运行音频设备测试：
   ```python
   import sounddevice as sd
   print(sd.query_devices())
   ```

#### 问题：音频权限被拒绝
```
❌ Permission denied: audio device
```

**解决方案：**
1. 检查应用程序麦克风权限
2. 以管理员身份运行
3. 重启音频服务：
   ```cmd
   net stop audiosrv
   net start audiosrv
   ```

### 3. 网络连接问题

#### 问题：无法访问HuggingFace
```
❌ Connection timeout: huggingface.co
```

**解决方案：**
1. 检查网络连接
2. 使用镜像源：
   ```python
   os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
   ```
3. 使用代理：
   ```python
   os.environ['HTTP_PROXY'] = 'http://proxy:port'
   os.environ['HTTPS_PROXY'] = 'http://proxy:port'
   ```

### 4. 依赖包问题

#### 问题：包版本冲突
```
❌ ImportError: cannot import name 'xxx'
```

**解决方案：**
1. 更新到兼容版本：
   ```bash
   pip install torch>=2.0.0 transformers>=4.35.0
   ```

2. 创建虚拟环境：
   ```bash
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

#### 问题：PyTorch CUDA版本不匹配
```
❌ CUDA version mismatch
```

**解决方案：**
1. 检查CUDA版本：
   ```bash
   nvidia-smi
   ```

2. 安装对应版本的PyTorch：
   ```bash
   # CUDA 11.8
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   
   # CUDA 12.1
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
   ```

### 5. 性能问题

#### 问题：翻译延迟过高 (>5秒)
**解决方案：**
1. 使用GPU加速
2. 减少音频缓冲区大小
3. 优化VAD参数：
   ```python
   VAD_CONFIG['threshold'] = 0.3  # 降低阈值
   VAD_CONFIG['min_speech_duration'] = 0.2  # 减少最小时长
   ```

#### 问题：CPU使用率过高
**解决方案：**
1. 启用GPU加速
2. 减少并发处理数量
3. 优化音频处理参数

### 6. Web界面问题

#### 问题：浏览器无法访问
```
❌ This site can't be reached
```

**解决方案：**
1. 检查防火墙设置
2. 确认端口5000未被占用：
   ```bash
   netstat -an | findstr :5000
   ```
3. 尝试其他端口：
   ```python
   WEB_CONFIG['port'] = 5001
   ```

#### 问题：WebSocket连接失败
**解决方案：**
1. 检查浏览器WebSocket支持
2. 禁用浏览器扩展
3. 尝试其他浏览器

### 7. 系统资源问题

#### 问题：内存不足
```
❌ MemoryError
```

**解决方案：**
1. 关闭其他应用程序
2. 增加虚拟内存
3. 使用较小的模型或CPU模式

#### 问题：磁盘空间不足
**解决方案：**
1. 清理临时文件
2. 移动模型缓存到其他磁盘
3. 删除不必要的模型版本

## 🔍 诊断工具

### 系统诊断脚本
```python
# 运行完整诊断
python check_hardware.py

# 检查特定组件
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
"
```

### 日志查看
```bash
# 查看应用日志
type logs\app.log

# 实时监控日志
powershell Get-Content logs\app.log -Wait
```

## 📞 获取帮助

如果以上解决方案都无法解决问题：

1. **收集信息：**
   - 运行 `python check_hardware.py`
   - 复制完整错误信息
   - 记录系统配置

2. **检查日志：**
   - 查看 `logs/app.log`
   - 记录错误发生时间

3. **联系支持：**
   - 提供详细的错误描述
   - 包含系统信息和日志
   - 说明重现步骤

## 💡 性能优化建议

1. **硬件优化：**
   - 使用SSD存储模型
   - 确保充足的GPU内存
   - 使用高速网络连接

2. **软件优化：**
   - 定期更新驱动程序
   - 使用最新版本的依赖包
   - 优化系统设置

3. **配置优化：**
   - 调整音频参数
   - 优化VAD设置
   - 合理设置缓存大小
